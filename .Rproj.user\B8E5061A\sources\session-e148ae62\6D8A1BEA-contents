# Async Project 10.07.25
#### Packages
library(tidyverse)
library(ggplot2)
#### Work
read.csv("Snowy_Lake.csv")
Snowy_Lake <- read.csv("Snowy_Lake.csv")
Partner_Income <- Snowy_Lake$Q15 # Ratio data income
RAS <- Snowy_Lake$SC1 / 7 # The Relationship Assessment Scale, 7 Questions, 5 Point Likert Scale. Questions 4 - 7 are reverse scored. Divide by 7 to get scores
RAS
Snowy_Lake %>%
  filter(Partner_Income < 400000) %>%
  ggplot(aes(x = Partner_Income, y = RAS)) +
  geom_jitter(alpha = .4, width = .2) +
  geom_point() +
  scale_x_continuous(breaks = seq(0, max(Partner_Income, na.rm = TRUE), by = 5000)) + 
  #This took about an hour to figure out and it still annoys me.
  labs(
    title = "Relationship Satisfaction based on Partner Income",
    x = "Partner Income in USD",
    y = "Relationship Satisfaction"
  )


  