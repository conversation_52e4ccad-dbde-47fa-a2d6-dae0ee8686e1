# Async Project 10.07.25
#### Packages
library(tidyverse)
library(ggplot2)
#### Work
read.csv("Snowy_Lake.csv")
Snowy_Lake <- read.csv("Snowy_Lake.csv")
Partner_Income <- Snowy_Lake$Q15 # Ratio data income
RAS <- Snowy_Lake$SC1 / 7 # The Relationship Assessment Scale, 7 Questions, 5 Point Likert Scale. Questions 4 - 7 are reverse scored. Divide by 7 to get scores
RAS
Snowy_Lake %>% 
  ggplot(aes(x = Partner_Income, y = RAS)) +
  geom_jitter(alpha = .4, width = .2) +
  geom_point() +
  labs(
    title = "Relationship Satisfaction based on Partner Income",
    x = "Partner Income in USD",
    y = "Relationship Satisfaction"
  )
ggsave(filename = "Snow_Lake_490.jpg")
