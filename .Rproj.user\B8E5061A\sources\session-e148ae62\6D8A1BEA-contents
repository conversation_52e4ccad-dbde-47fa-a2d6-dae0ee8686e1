# Async Project 10.07.25
#### Packages
library(tidyverse)
library(ggplot2)
#### Work
read.csv("Snowy_Lake.csv")
Snowy_Lake <- read.csv("Snowy_Lake.csv")
Partner_Income <- Snowy_Lake$Q15 # Ratio data income
RAS <- Snowy_Lake$SC1 / 7 # The Relationship Assessment Scale, 7 Questions, 5 Point Likert Scale. Questions 4 - 7 are reverse scored. Divide by 7 to get scores
is.na(Snowy_Lake$Q15)
Snowy_Lake %>%
  filter(Q15 <400000) %>% # PAIN IN THE ASS! Kept using Partner_Income instead of Snowy_Lake$Q15
  ggplot(aes(x = Q15, y = SC1/7)) +
  geom_point() +
  scale_x_continuous(breaks = seq(0, max(Snowy_Lake$Q15, na.rm = TRUE), by = 15000)) + #I hate this. This took 30 minutes to figure out, 15 just to figure out that I had to add na.rm = TRUE. Learned about is.na, didn't help figure out why I needed na.rm.
  labs(
    title = "Relationship Satisfaction based on Partner Income",
    x = "Partner Income in USD",
    y = "Relationship Satisfaction"
  )


  