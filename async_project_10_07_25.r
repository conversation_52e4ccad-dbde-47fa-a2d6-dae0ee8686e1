# Async Project 10.07.25

# Packages ----------------------------------------------------------------
library(tidyverse)
library(ggplot2)

# Loading in variables ----------------------------------------------------

read.csv("Snowy_Lake.csv")
Snowy_Lake <- read.csv("Snowy_Lake.csv")

# Basic stuff -------------------------------------------------------------

Partner_Income <- Snowy_Lake$Q15 # Ratio data income. 
mean(Snowy_Lake$Q6) # This sample is so weird. We ran it on mostly college students but the average age is 30. I can think of only a couple subjects that I gave this to that were over 50. Most were mid to late 20s.
annoying <- Snow<PERSON>_Lake$Age_Buckets

RAS <- Snowy_Lake$SC1 / 7 # The Relationship Assessment Scale, 7 Questions, 5 Point Likert Scale. Questions 4 - 7 are reverse scored. Divide by 7 to get scores

# Plot Code ---------------------------------------------------------------

Snowy_Lake %>%
  filter(Q15 <400000) %>% # PAIN IN THE ASS! Kept using Partner_Income instead of Snowy_Lake$Q15
  ggplot(aes(x = Q15, y = SC1/7)) +
  geom_point() +
  scale_x_continuous(breaks = seq(0, 250000, by = 15000)) +  #Also a pain in the ass. Had to go back to Homework 1 to figure out how to use seq() again. (https://youtu.be/-SuK8G2d-Vs?si=s6ms14EBECoRHpA8)
  labs(
    title = "Relationship Satisfaction based on Partner Income",
    x = "Partner Income in USD",
    y = "Relationship Satisfaction"
  )




  