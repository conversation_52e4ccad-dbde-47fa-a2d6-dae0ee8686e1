## CSUDH PSY 495-01: Social Data Science
## Instructor: <PERSON>, <PERSON>.<PERSON>.
## Homework 3

## Instructions
# For problems that require you to write code, write your code in the appropriate section below
# For problems that require you to provide a written response, write your answer as a comment (#)

# YOUR NAME:<PERSON>


#############
### PROBLEM 0

# Load in the tidyverse
library(tidyverse)


#############
### PROBLEM 1


## 1a. Want free data? Run the following lines of code. What is chickwts?
# Chickwts is a data set with two variables 'weight' and 'feed,' each having 71 seperate data points

?chickwts # read the description of the study the data come from
chickwts <- chickwts


## 1b. Index each column of chickwts by name. 
#       In a comment, describe what TYPE of variable each column is.
chickwts [, c(1,2)] #weight = numeric, Feed = string


## 1c. Apply the class() function to each column in chickwts.
#       In a comment, compare the results to your answers in 1b
class(chickwts$weight) # Same as 1B
class(chickwts$feed) # Different from IB. Data type if Factor



#############
### PROBLEM 2

## 2a. Use the count() function we used in class Weeks 4 & 5 to find out
#       how many chickens received each type of feed.

chickwts %>% 
  count(feed)

## 2b. Find the mean weight of all chickens in chickwts (regardless of feed).
#       Save as a variable (you can call it mean_weight)

mean_weights <- mean(chickwts$weight)

## 2c. Filter the dataframe to return only the rows where weight is less than mean_weight.

chickwts %>% 
  filter(weight< mean_weights)


## 2d. Filter the dataframe to return only the rows where feed is "sunflower"
chickwts %>% 
  filter(feed == "sunflower")


## 2e. Filter the dataframe to return only the rows where feed is "sunflower" AND ALSO
#         the weight is less than mean_weight
chickwts %>% 
  filter(weight < mean_weights, feed == "sunflower")


## 2f. Use group_by() and summarize() functions like we did in Week 4 in order to
#         find the mean weight for each feed (separately)
chickwts %>% 
  group_by(feed) %>% 
  summarise(feed_MW = mean(weight)) %>% 
  arrange(feed_MW)

## 2g. In a comment, describe how the mean chicken weight of those who ate 
#         sunflower seeds (2f) compares to the overall mean (2b).
329/mean_weights
# Chickens who ate sunflower seed were on 1.26 times more heavy than the average weight of all chickens combined 
#############
### PROBLEM 3

## 3a. Use the ggplot() function like we did in Week 5 in order to
#         make a jitter plot with feed on x-axis and weight on y-axis
chickwts %>% 
  ggplot(aes(x = feed, y = weight)) + 
  geom_boxplot() +
  geom_jitter(alpha = .4, width = .2) +
  xlab("Feed Type") +
  ylab("Weight of Chickens") +
  ggtitle("Weight of Chicken by Feed Type")
ggsave(filename = "chickwts_plot.pdf",width = 4, height=3)
  

## 3b. In a comment, compare the weights for sunflower to horsebean.
# Purely based on visual analysis, chickens feed sunflower feed are much more likely to be heavier than those feed horsebean feed.


##### Mistakes, what you learned, etc.
# Arrange can order from least to most
# A data type called 'factor' exists
#https://youtu.be/IJbViWzhIQo?si=veqmUC6PRZdT9o7Q
#https://www.youtube.com/watch?v=owkZgyeNXSg
